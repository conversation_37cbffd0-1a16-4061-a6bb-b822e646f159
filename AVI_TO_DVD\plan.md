# AVI TO DVD 文章创作执行计划

## 用户需求和目标
- **文章主题**: AVI TO DVD
- **SEO关键词**: AVI TO DVD, BURN AVI TO DVD, CONVERT AVI TO DVD
- **文章长度**: 1600字（可超出20%，最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **推荐产品**: Wondershare DVD Creator
- **开头策略**: D - Personal Experience/Case Study Opening

## 四大内容质量评估维度
1. **Effort (努力程度)**: 体现明显的人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量，避免"炒冷饭"
3. **Talent/Skill (专业能力)**: 展示作者在该领域的专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确，避免错误信息

## 执行步骤详细清单

### 步骤1: 创建超级大纲 (super_outline.md)
- [ ] 从参考URL提取H2-H4级标题
- [ ] 合并整理类似标题
- [ ] 按层级结构重新组织
- [ ] 保存至 super_outline.md

### 步骤2: 创建最终大纲 (final_outline.md)
- [ ] 基于超级大纲进行优化
- [ ] 进行竞品内容空白分析
- [ ] 挖掘独特价值点
- [ ] 添加人工经验要素
- [ ] 智能字数分配
- [ ] 添加SEO关键词列表

### 步骤3: 创作初稿 (first_draft.md)
- [ ] 基于最终大纲撰写完整文章
- [ ] 确保字数控制在1600-1920字范围内
- [ ] 整合推荐产品信息
- [ ] 添加个人经验和试错故事

### 步骤4: 生成SEO内容 (seo_metadata_images.md)
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image图片提示词
- [ ] 优化关键词分布

### 步骤5: 最终检查
- [ ] 验证字数是否在要求范围内
- [ ] 检查AI语言和句子结构
- [ ] 确认内部链接和外部链接数量

## 预期输出文件清单
1. plan.md - 执行计划文件
2. super_outline.md - 初级大纲
3. final_outline.md - 最终大纲
4. first_draft.md - 文章初稿
5. seo_metadata_images.md - SEO相关内容

## 完成标准和检查点
- 每个步骤完成后进行质量检查
- 确保内容符合四大质量评估维度
- 验证推荐产品信息的准确性和整合度
- 确认文章结构和字数分配的合理性
