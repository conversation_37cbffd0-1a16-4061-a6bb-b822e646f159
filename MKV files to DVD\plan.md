# MKV Files to DVD - Article Creation Plan

## 用户需求和目标
- **主题**: MKV files to DVD
- **SEO关键词**: MKV files to DVD, BURN MKV files to DVD, CONVERT MKV files to DVD
- **字数要求**: 1600字（可超出20%，最多1920字）
- **语言**: English
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **推荐产品**: Wondershare DVD Creator
- **开头策略**: A (Surprising Statistic/Fact Opening)

## 执行步骤详细清单

### 第一阶段：大纲创建
1. ✅ 提取参考URL内容和标题结构
2. ⏳ 创建超级大纲 (super_outline.md)
3. ⏳ 竞品内容分析和信息增量识别
4. ⏳ 生成最终大纲 (final_outline.md)

### 第二阶段：内容创作
5. ⏳ 撰写初稿 (first_draft.md)
6. ⏳ 内容质量检查和优化

### 第三阶段：SEO优化
7. ⏳ 生成SEO标题和元描述
8. ⏳ 创建featured image提示词
9. ⏳ 保存SEO内容 (seo_metadata_images.md)

### 第四阶段：最终检查
10. ⏳ 字数验证（1600-1920字范围）
11. ⏳ AI语言检查和人性化调整
12. ⏳ 内外链数量验证

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 包含至少3个竞品文章未涵盖的独特观点
- [ ] 每个H2章节准备了人工经验要素
- [ ] 识别并准备解决用户的具体痛点
- [ ] 字数分配总和在1600-1920字范围内
- [ ] 核心推荐产品章节获得20-25%字数分配

### 内容创作检查点
- [ ] 文章体现明显的人工成分和深度思考
- [ ] 提供独特信息增量，避免"炒冷饭"
- [ ] 展示作者在该领域的专业知识和实际经验
- [ ] 确保事实准确，避免错误信息
- [ ] 包含个人见解和试错故事

### SEO优化检查点
- [ ] SEO关键词自然分布
- [ ] 内部链接和外部链接数量达标
- [ ] 元描述和标题符合SEO最佳实践
- [ ] Featured image提示词具体且相关

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO内容和图片提示词

## 质量评估维度
- **Effort (努力程度)**: 体现明显的人工成分和深度思考
- **Originality (原创性)**: 提供独特信息增量
- **Talent/Skill (专业能力)**: 展示专业知识和实际经验
- **Accuracy (准确性)**: 确保事实准确

## 信息增量要求
- 至少3-5个其他文章未涵盖的独特观点或解决方案
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

## 推荐产品集成策略
- 从用户角度出发，先说明官方功能或免费工具的能力
- 明确用户在使用过程中遇到的具体限制或不足
- 引入第三方工具时，强调它们是补充而非替代
- 聚焦真实的使用场景
- 保持友好、实用的语气，以解决问题为核心
